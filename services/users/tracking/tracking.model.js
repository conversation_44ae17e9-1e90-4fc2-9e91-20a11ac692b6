const mongoose = require("mongoose");
const { Schema } = require("mongoose");
const { TRACKING, USER } = require("../../../constants/dbCollections");

const schema = new Schema(
  {
    userId: { type: Schema.Types.ObjectId, ref: USER },
    actionName: { type: String },
    browser: { type: String },
    ipAddress: { type: String },
    lang: { type: String },
    isDeleted: { type: Boolean, default: false },
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
    versionKey: false,
  },
);

// Add indexes for performance optimization
schema.index({ createdAt: 1 }); // Index for date range queries in reportTracking
schema.index({ userId: 1 }); // Index for grouping by userId
schema.index({ createdAt: 1, userId: 1 }); // Compound index for aggregation pipeline
schema.index({ updatedAt: -1 }); // Index for lastVisit calculation
schema.index({ isDeleted: 1, createdAt: 1 }); // Compound index for filtered date queries

module.exports = mongoose.model(TRACKING, schema, TRACKING);

const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');

const {ROL<PERSON>, USER, IMAGE, ORGANIZATION} = require('../../constants/dbCollections');
const {PERSONA} = require('../../constants/constant');
const {encryptPassword} = require("./users.helper");

const {Schema} = mongoose;
const userSchema = new Schema({
  fullName: {
    type: String,
    trim: true,
    required: true,
    "default": ""
  },
  email: {
    type: String,
    trim: true,
    lowercase: true
  },
  password: {
    type: String,
    required: "Please fill in a password"
  },
  gender: {type: String},
  phone: {type: String},
  avatar: {type: String},
  imageAvatarId: {type: Schema.Types.ObjectId, ref: IMAGE},
  organizationId: {type: Schema.Types.ObjectId, ref: ORGANIZATION},
  isDeleted: {type: Boolean, default: false, select: false},
  roleId: [{type: Schema.Types.ObjectId, ref: ROL<PERSON>}],
  isSystemAdmin: {type: Boolean, default: false},
  active: {type: Boolean, default: false},
  neverLogin: {type: Boolean, default: true},
  lastLogin: {type: Date},
  lastVisit: {type: Date},
  lastChangePassword: {type: Date, default: new Date()},
  deviceTokens: [],
  role: {
    type: String,
    enum: ["admin", "normal", "contributor"],
    default: "normal"
  },
  type: {
    type: String,
    enum: ["student", "teacher"],
    // default: "student"
  },
  persona: [{
    type: String,
    enum: Object.values(PERSONA),
    default: "other"
  }],
  hearAboutUs: {
    type: Schema.Types.Mixed,
  },
  isDeveloper: {type: Boolean, default: false},
  state: {
    type: String,
    enum: ["active", "waitlist"],
    default: "active"
  },
  hasPassword: {type: Boolean, default: true},
}, {
  timestamps: {
    createdAt: 'createdAt', updatedAt: 'updatedAt',
  }, collation: {locale: 'vi'}, versionKey: false,
});

userSchema.pre('save', function (next) {
  let user = this;
  // only hash the password if it has been modified (or is new)
  if (!user.isModified('password')) return next();
  user.password = encryptPassword(user.password);
  next();
});

// Add indexes for performance optimization
userSchema.index({ isDeleted: 1, active: 1 }); // Compound index for main query filter
userSchema.index({ email: 1 }); // Index for email search
userSchema.index({ fullName: 1 }); // Index for fullName search
userSchema.index({ isDeveloper: 1 }); // Index for developer filter
userSchema.index({ createdAt: 1 }); // Index for date range queries
userSchema.index({ lastVisit: -1 }); // Index for sorting by lastVisit
userSchema.index({ isDeleted: 1, active: 1, createdAt: 1 }); // Compound index for date filtering

userSchema.plugin(mongoosePaginate);

module.exports = mongoose.model(USER, userSchema, USER);

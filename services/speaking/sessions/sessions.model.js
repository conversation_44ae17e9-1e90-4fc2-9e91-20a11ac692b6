"use strict";

const mongoose = require("mongoose");
const {Schema} = require("mongoose");
const {SPEAKING_PARTS, SESSION_STATUS} = require("../speaking.constants");
const {SPEAKING_SESSIONS, USER, FILE} = require("../../../constants/dbCollections");

// Schema for speaking sessions
const schema = new Schema(
  {
    name: {
      type: String,
      default: "Untitled speech"
    },
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: USER,
      required: true,
      index: true
    },
    mode: {
      type: String,
      enum: ["practice", "real_test"],
      required: true
    },
    tag: String,
    part: {
      type: String,
      enum: Object.values(SPEAKING_PARTS),
      required: true,
      index: true
    },
    startTime: {
      type: Date,
      default: Date.now
    },
    endTime: {
      type: Date
    },
    status: {
      type: String,
      enum: Object.values(SESSION_STATUS),
      default: SESSION_STATUS.IN_PROGRESS
    },
    totalDuration: {type: Number},
    gptModel: {type: String},
    completionTokens: {type: Number},
    promptTokens: {type: Number},
    totalTokens: {type: Number},
    resultSummary: {
      vocabularyScore: {
        type: Number,
        min: 0,
        max: 9
      },
      grammarScore: {
        type: Number,
        min: 0,
        max: 9
      },
      pronScore: {
        type: Number,
        min: 0,
        max: 9
      },
      fluencyScore: {
        type: Number,
        min: 0,
        max: 9
      },
      overall: {
        type: Number,
        min: 0,
        max: 9
      },
    },
    feedback: String,

    isDeleted: {
      type: Boolean,
      default: false
    }
  },
  {
    timestamps: true,
    versionKey: false,
  }
);

// Create indexes
schema.index({userId: 1, createdAt: -1});
schema.index({status: 1, userId: 1, tag: 1}); // Optimized index for completion checks
schema.index({tag: 1, status: 1}); // For topic-based queries
schema.index({isDeleted: 1, status: 1, createdAt: -1}); // For tracking queries
schema.index({"userId._id": 1, status: 1}); // For aggregation grouping by userId

module.exports = mongoose.model(SPEAKING_SESSIONS, schema, SPEAKING_SESSIONS);

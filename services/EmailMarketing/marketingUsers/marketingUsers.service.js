const DbMongoose = require('../../../mixins/dbMongo.mixin');
const Model = require('./marketingUsers.model');
const {MoleculerClientError} = require('moleculer').Errors;
const i18next = require('i18next');
const BaseService = require('../../../mixins/baseService.mixin');
const FunctionsCommon = require('../../../mixins/functionsCommon.mixin');
const FileMixin = require('../../../mixins/file.mixin');
const ExcelJS = require('exceljs');
const fs = require('fs');
const path = require('path');

module.exports = {
  name: 'mktusers',
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon, FileMixin],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {
      'mktGroupId': 'mktgroups.get',
    },
  },

  hooks: {},

  actions: {
    /**
     * Upload Excel file and import marketing users
     */
    uploadExcel: {
      auth: "required",
      async handler(ctx) {
        const {mktGroupId} = ctx.meta.$multipart;

        if (!mktGroupId) {
          throw new MoleculerClientError('mktGroupId is required', 400);
        }

        // Verify that the marketing group exists
        const group = await this.broker.call('mktgroups.get', {id: mktGroupId});
        if (!group) {
          throw new MoleculerClientError('Marketing group not found', 404);
        }
        // Process Excel file directly from stream/buffer
        const result = await this.processExcelFromStream(ctx.params, mktGroupId);

        return result;
      }
    },

    /**
     * Get users by marketing group ID
     */
    getUsersByGroup: {
      rest: {
        method: 'GET',
        path: '/:mktGroupId/users'
      },
      params: {
        mktGroupId: {type: 'string'},
      },
      async handler(ctx) {
        const {mktGroupId} = ctx.params;
        return await this.adapter.find({
          query: {
            mktGroupId: mktGroupId,
            isDeleted: false
          }
        });
      }
    },

    /**
     * Delete user from marketing group
     */
    deleteUser: {
      rest: {
        method: 'DELETE',
        path: '/:id'
      },
      params: {
        id: {type: 'string'}
      },
      async handler(ctx) {
        const {id} = ctx.params;

        const user = await this.adapter.findById(id);
        if (!user) {
          throw new MoleculerClientError('Marketing user not found', 404);
        }

        // Soft delete
        return await this.adapter.updateById(id, {isDeleted: true});
      }
    },

    /**
     * Get statistics for a marketing group
     */
    getGroupStats: {
      rest: {
        method: 'GET',
        path: '/:mktGroupId/stats'
      },
      params: {
        mktGroupId: {type: 'string'}
      },
      async handler(ctx) {
        const {mktGroupId} = ctx.params;

        const totalUsers = await this.adapter.count({
          mktGroupId: mktGroupId,
          isDeleted: false
        });

        return {
          mktGroupId,
          totalUsers
        };
      }
    },

    /**
     * Download Excel template for user import
     */
    downloadTemplate: {
      rest: {
        method: 'GET',
        path: '/template'
      },
      async handler(ctx) {
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Marketing Users Template');

        // Add headers
        const headers = ['email', 'name', 'phone'];
        worksheet.addRow(headers);

        // Add sample data
        worksheet.addRow(['<EMAIL>', 'John Doe', '+1234567890']);
        worksheet.addRow(['<EMAIL>', 'Jane Smith', '+0987654321']);

        // Style the header row
        worksheet.getRow(1).font = {bold: true};
        worksheet.getRow(1).fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: {argb: 'FFE0E0E0'}
        };

        // Auto-fit columns
        worksheet.columns.forEach(column => {
          column.width = 20;
        });

        // Set response headers for file download
        ctx.meta.$responseHeaders = {
          'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'Content-Disposition': 'attachment; filename=marketing-users-template.xlsx'
        };

        // Write to buffer and return
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer;
      }
    }
  },

  methods: {
    /**
     * Process Excel file from stream/buffer and import users
     */
    async processExcelFromStream(stream, mktGroupId) {
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.read(stream);

      const worksheet = workbook.getWorksheet(1); // Get first worksheet
      if (!worksheet) {
        throw new MoleculerClientError('Excel file is empty or invalid', 400);
      }

      // Get header row to find column indices
      const headerRow = worksheet.getRow(1);
      const headers = {};
      headerRow.eachCell((cell, colNumber) => {
        const value = cell.value?.toString().toLowerCase().trim();
        if (value === 'email') headers.email = colNumber;
        if (value === 'name') headers.name = colNumber;
        if (value === 'phone') headers.phone = colNumber;
      });
      // Validate required columns exist
      if (!headers.email || !headers.name || !headers.phone) {
        throw new MoleculerClientError(
          'Excel file must contain columns: email, name, phone',
          400
        );
      }

      const results = {
        total: 0,
        success: 0,
        failed: 0,
        duplicate: 0,
        errors: [],
        data: [] // Array chứa tất cả dữ liệu với thông tin lỗi
      };

      // Process each row (skip header)
      for (let rowNumber = 2; rowNumber <= worksheet.rowCount; rowNumber++) {
        const row = worksheet.getRow(rowNumber);

        // Skip empty rows
        if (!row.hasValues) continue;

        results.total++;

        try {
          const email = row.getCell(headers.email).value?.toString().trim();
          const name = row.getCell(headers.name).value?.toString().trim();
          const phone = row.getCell(headers.phone).value?.toString().trim();

          // Tạo object dữ liệu cho dòng này
          const rowData = {
            row: rowNumber,
            email: email || '',
            name: name || '',
            phone: phone || '',
            status: 'pending',
            error: null
          };

          // Validate required fields
          if (!email || !name || !phone) {
            rowData.status = 'failed';
            rowData.error = 'Missing required fields (email, name, or phone)';
            results.failed++;
            results.errors.push({
              row: rowNumber,
              error: rowData.error
            });
            results.data.push(rowData);
            continue;
          }

          // Validate email format
          if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
            rowData.status = 'failed';
            rowData.error = 'Invalid email format';
            results.failed++;
            results.errors.push({
              row: rowNumber,
              error: rowData.error
            });
            results.data.push(rowData);
            continue;
          }

          // Check for duplicate in the same group
          const existingUser = await this.adapter.findOne({
            email: email,
            mktGroupId: mktGroupId,
            isDeleted: false
          });

          if (existingUser) {
            rowData.status = 'duplicate';
            rowData.error = 'Email already exists in this marketing group';
            results.duplicate++;
            results.errors.push({
              row: rowNumber,
              error: rowData.error
            });
            results.data.push(rowData);
            continue;
          }

          // Insert user
          const insertedUser = await this.adapter.insert({
            email,
            name,
            phone,
            mktGroupId
          });

          rowData.status = 'success';
          rowData.error = null;
          rowData.userId = insertedUser._id;
          results.success++;
          results.data.push(rowData);

        } catch (error) {
          const rowData = {
            row: rowNumber,
            email: row.getCell(headers.email).value?.toString().trim() || '',
            name: row.getCell(headers.name).value?.toString().trim() || '',
            phone: row.getCell(headers.phone).value?.toString().trim() || '',
            status: 'failed',
            error: error.message
          };

          results.failed++;
          results.errors.push({
            row: rowNumber,
            error: error.message
          });
          results.data.push(rowData);
        }
      }

      return results;
    }
  },

  events: {}
};
